<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  message,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { announcementNoticeApi } from '@haierbusiness-front/apis';
import {
  IAnnouncementNoticeFilter,
  IAnnouncementNotice
} from '@haierbusiness-front/common-libs';
import {
  announcementNoticeStateOptions,
  AnnouncementContentForm,
  announcementContentFormOptions,
  AnnouncementEffectScope,
  announcementEffectScopeOptions
} from '@haierbusiness-front/common-libs';
import { computed, ref, onMounted, nextTick, reactive, h, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'
import router from '../../router'
import ColumnFilter from '@haierbusiness-front/components/mice/search/ColumnFilter.vue';
import dayjs, { Dayjs } from 'dayjs';
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  // 初始加载数据
  listApiRun({
    effectScope:1,
    state: '正常',
    pageNum: 1,
    pageSize: 10
  });
})

const columns: ColumnType[] = [
  {
    title: '序号',
    width: '80px',
    align: 'center',
    customRender: ({ index }) => index + 1,
  },
  {
    title: '通知标题',
    dataIndex: 'title',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '内容形式',
    dataIndex: 'contentForm',
    width: '120px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      if (text === AnnouncementContentForm.TEXT) {
        return '文本';
      } else if (text === AnnouncementContentForm.LINK) {
        return '链接';
      }
      return text;
    },
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: '100px',
    align: 'center',
  },
  {
    title: '弹窗通知',
    dataIndex: 'isWindow',
    width: '100px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      return text ? '是' : '否';
    },
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IAnnouncementNoticeFilter & {
  title?: string;
  contentForm?: string | number;
  effectScope?: string;
  sort?: number;
  state?: string;
  isWindow?: boolean;
  createName?: string;
  gmtCreate?: string;//创建开始
  gmtCreateEnd?: string;//创建结束
}>({})

const CreateTime = ref<[Dayjs, Dayjs]>()
watch(() => CreateTime.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.gmtStartCreate = dayjs(n[0]).format('YYYY-MM-DD 00:00:00');
    searchParam.value.gmtEndCreate = dayjs(n[1]).format('YYYY-MM-DD 23:59:59');
  } else {
    searchParam.value.gmtStartCreate = undefined;
    searchParam.value.gmtEndCreate = undefined;
  }
});

// 内容形式选项
const contentFormOptions = announcementContentFormOptions;

// 通知作用范围选项
const effectScopeOptions = announcementEffectScopeOptions;

// 状态选项
const stateOptions = announcementNoticeStateOptions;

const {
  data,
  run: listApiRun,
  loading,
} = usePagination(announcementNoticeApi.list);

const reset = () => {
  searchParam.value = {
    title: undefined,
    contentForm: undefined,
    effectScope: undefined,
    sort: undefined,
    state: undefined,
    isWindow: undefined,
    createName: undefined,
  }
  CreateTime.value = undefined
  console.log('重置后的搜索参数:', searchParam.value);

  // 使用nextTick确保DOM更新后再调用接口
  nextTick(() => {
    // 重置后立即查询
    const params = {
      pageNum: 1,
      pageSize: 10
    };
    listApiRun(params);
  });
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pagination: any,
) => {
  // 确保使用最新的搜索参数和过滤参数，优先使用filterInputs中的非空值
  const params = {
    ...searchParam.value,
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
  };

  console.log('最终查询参数:', params);
  listApiRun(params);
};
const { visible, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<IAnnouncementNotice, IAnnouncementNotice>(announcementNoticeApi, "公告通知", () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  }))
//详情数据
const editData = ref<IAnnouncementNotice>()
  const confirmLoading = ref(false);
//通知详情
const noticeDetails = async (id:number | undefined | null)=>{
  if (!id) return;

  confirmLoading.value = true;
  try {
    const res = await announcementNoticeApi.details(id);
    editData.value = res;
    if(editData.value){
      if(res.contentForm == 2){
        window.open(res.informContent, '_blank');
      }else{
        visible.value = true
      }
    }
  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    confirmLoading.value = false;
  }
}
const thisHandleEdit = (item: IAnnouncementNotice) => {
  noticeDetails(item.id)
}



</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="title">通知标题：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.title" placeholder="请输入通知标题" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="contentForm">内容形式：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.contentForm" placeholder="请选择内容形式" style="width: 100%" allow-clear>
              <h-select-option v-for="item in contentFormOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createName">创建人：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.createName" placeholder="请输入创建人" style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createName">排序：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.sort" placeholder="请输入排序" style="width: 100%" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">

          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createName">弹窗通知：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.isWindow" placeholder="请选择弹窗通知" style="width: 100%" allow-clear>
              <h-select-option :value="true">是</h-select-option>
              <h-select-option :value="false">否</h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createName">创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="CreateTime" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="thisHandleEdit(record)">查看</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    <!-- 查看通知 -->
    <div v-if="visible">
      <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleShow">
      </edit-dialog>
    </div>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}
</style>
