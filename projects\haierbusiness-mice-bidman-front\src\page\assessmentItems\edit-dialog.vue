<script lang="ts" setup>
import {
  Select as hSelect,
  SelectOption as hSelectOption,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  InputNumber as hInputNumber,
  Textarea as hTextarea,
} from 'ant-design-vue';
import { computed, ref, watch, onMounted } from 'vue';
import type { Ref } from 'vue';
import { IAssessmentItems } from '@haierbusiness-front/common-libs';
import {
  SupplierType,
  SupplierTypeLabel,
} from '@haierbusiness-front/common-libs/src/micebidman/constant/AssessmentItemsType';
import { assessmentItemsApi } from '@haierbusiness-front/apis';

interface Props {
  show: boolean;
  data: IAssessmentItems | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: IAssessmentItems = {
  id: null,
  name: '',
  detail: '',
  score: 0,
  money: 0,
  type: undefined,
};

// 将服务商类型枚举转换为下拉选项
const supplierTypeOptions = Object.entries(SupplierTypeLabel).map(([value, label]) => ({
  label,
  value: Number(value),
}));

const rules = {
  name: [
    { required: true, message: '请输入考核条目名称', trigger: 'blur' },
    { max: 50, message: '考核条目名称最多50个字符', trigger: 'blur' },
  ],
  detail: [
    { required: true, message: '请输入考核明细', trigger: 'blur' },
    { max: 200, message: '考核明细最多200个字符', trigger: 'blur' },
  ],
  type: [{ required: true, message: '请选择服务商类型', trigger: 'change' }],
  score: [{ type: 'number', message: '考核分数必须为数字', trigger: 'blur' }],
  money: [
    { type: 'number', message: '考核金额必须为数字', trigger: 'blur' },
    { type: 'number', max: 0, message: '考核金额必须为负数', trigger: 'change' },
  ],
};

const assessmentItems: Ref<IAssessmentItems> = ref(Object.assign({}, defaultData, props.data || {}));

onMounted(() => {
  if (props.data && props.data.type) {
    if (typeof props.data.type === 'string') {
      assessmentItems.value.type = Number(props.data.type);
    }
  }
});

watch(props, (newValue) => {
  const newData = Object.assign({}, defaultData, newValue.data || {});
  if (newData.type && typeof newData.type === 'string') {
    newData.type = Number(newData.type);
  }

  assessmentItems.value = newData;
});

const emit = defineEmits(['cancel', 'ok']);

const visible = computed(() => props.show);

const handleOk = () => {
  confirmLoading.value = true;
  from.value
    .validate()
    .then(() => {
      // 获取所有表单值
      const formValues = {
        id: assessmentItems.value.id,
        name: assessmentItems.value.name,
        detail: assessmentItems.value.detail,
        type: assessmentItems.value.type,
        score: assessmentItems.value.score || 0,
        money: assessmentItems.value.money || 0,
      };

      console.log('表单提交的值：', formValues);

      // 不再直接调用API，而是将验证后的数据传递给父组件
      emit('ok', formValues, () => {
        confirmLoading.value = false;
      });
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
</script>

<template>
  <h-modal
    :visible="visible"
    :title="assessmentItems.id ? '编辑考核条目维护' : '新增考核条目维护'"
    :width="600"
    @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
  >
    <h-form
      ref="from"
      :model="assessmentItems"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
      :rules="rules"
      :hide-required-mark="true"
    >
      <h-form-item label="考核条目名称" name="name">
        <h-input v-model:value="assessmentItems.name" placeholder="请输入考核条目名称" :maxlength="50" show-count />
      </h-form-item>

      <h-form-item label="考核明细" name="detail">
        <h-textarea
          v-model:value="assessmentItems.detail"
          placeholder="请输入考核明细，最多200字"
          :maxlength="200"
          :auto-size="{ minRows: 3, maxRows: 6 }"
          show-count
        />
      </h-form-item>

      <h-form-item label="服务商类型" name="type">
        <h-select v-model:value="assessmentItems.type" placeholder="请选择服务商类型" style="width: 100%">
          <h-select-option v-for="option in supplierTypeOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </h-select-option>
        </h-select>
      </h-form-item>

      <h-form-item label="考核分数" name="score">
        <h-input-number
          v-model:value="assessmentItems.score"
          placeholder="请输入考核分数(非必填)"
          :precision="1"
          style="width: 100%"
        >
          <template #addonAfter>分</template>
        </h-input-number>
      </h-form-item>

      <h-form-item label="考核金额" name="money">
        <h-input-number
          v-model:value="assessmentItems.money"
          placeholder="请输入考核金额(非必填)"
          :precision="2"
          style="width: 100%"
        >
          <template #addonAfter>元</template>
        </h-input-number>
      </h-form-item>
    </h-form>
  </h-modal>
</template>

<style lang="less" scoped>
.important {
  color: red;
}
</style>